import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";
import { motion } from "framer-motion";
import axios from "axios";

const initialQuestions = [
  "Apa yang memotivasi Anda untuk bekerja di bidang ini?",
  "Ceritakan pengalaman ketika Anda berada dalam tekanan tinggi.",
  "Bagaimana Anda memastikan hasil kerja Anda tetap teliti?",
  "Bagaimana Anda menyelesaikan tugas dengan tenggat waktu yang ketat?",
  "Ceritakan cara Anda menjaga kualitas dalam pekerjaan sehari-hari.",
  "Ceritakan bagaimana Anda mengatur pekerjaan secara sistematis.",
  "Pernahkah Anda meyakinkan orang lain untuk menerima ide Anda? Bagaimana caranya?",
  "Ceritakan bagaimana Anda beradaptasi dengan lingkungan kerja yang baru.",
  "Ceritakan pengalaman kerja tim yang paling berkesan bagi Anda.",
  "Apa yang Anda lakukan ketika diminta mematuhi aturan yang tidak Anda setujui?",
  "Ceritakan bagaimana Anda mengambil peran kepemimpinan dalam sebuah proyek."
];

const categories = [
  "Motivasi kerja",
  "Stabilitas Emosi",
  "Toleransi Stress",
  "Ketelitian Kerja",
  "Tempo Kerja",
  "Orientasi Kualitas",
  "Sistematika Kerja",
  "Meyakinkan orang lain",
  "Penyesuaian Diri",
  "Kerjasama",
  "Kepatuhan kerja",
  "Kepemimpinan"
];

export default function AIInterview() {
  const [step, setStep] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [result, setResult] = useState(null);
  const [recording, setRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);

  const handleStartRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream);
    const audioChunks = [];

    recorder.ondataavailable = (event) => {
      audioChunks.push(event.data);
    };

    recorder.onstop = async () => {
      const audioBlob = new Blob(audioChunks, { type: "audio/webm" });
      const formData = new FormData();
      formData.append("audio", audioBlob);

      try {
        const response = await axios.post("http://localhost:5000/transcribe", formData);
        const transcribedText = response.data.transcript;
        const updatedAnswers = [...answers, { question: initialQuestions[step], answer: transcribedText }];
        setAnswers(updatedAnswers);

        if (step < initialQuestions.length - 1) {
          setStep(step + 1);
        } else {
          evaluateAnswers(updatedAnswers);
        }
      } catch (error) {
        console.error("Transkripsi gagal:", error);
      }
    };

    recorder.start();
    setMediaRecorder(recorder);
    setRecording(true);
  };

  const handleStopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setRecording(false);
    }
  };

  const evaluateAnswers = (responses) => {
    const mockScore = categories.map((cat) => {
      const value = Math.floor(Math.random() * 31) + 70;
      return { name: cat, value };
    });
    setResult(mockScore);
  };

  const getLevel = (value) => {
    if (value < 70) return "Belum memenuhi standard";
    if (value < 80) return "Memenuhi Standard Minimum";
    return "Memenuhi Standard";
  };

  return (
    <div className="max-w-2xl mx-auto p-4 space-y-6">
      <motion.h1 className="text-2xl font-bold text-center" initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
        AI Interviewer - Capture
      </motion.h1>
      {!result ? (
        <Card>
          <CardContent className="space-y-4 p-4">
            <p className="font-medium">{initialQuestions[step]}</p>
            <div className="space-x-2">
              {!recording ? (
                <Button onClick={handleStartRecording}>Mulai Rekam</Button>
              ) : (
                <Button onClick={handleStopRecording}>Stop & Kirim</Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-4">
            <h2 className="text-lg font-semibold mb-2">Hasil Wawancara</h2>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={result}>
                <XAxis dataKey="name" fontSize={10} interval={0} angle={-30} />
                <YAxis domain={[0, 100]} />
                <Tooltip formatter={(value) => `${value}%`} />
                <Bar dataKey="value" />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {result.map((r, idx) => (
                <p key={idx} className="text-sm">
                  <strong>{r.name}:</strong> {r.value}% - <em>{getLevel(r.value)}</em>
                </p>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
