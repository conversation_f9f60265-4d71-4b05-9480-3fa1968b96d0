from flask import Flask, request, jsonify
from flask_cors import CORS
import whisper
import tempfile

app = Flask(__name__)
CORS(app)

model = whisper.load_model("base")

@app.route('/transcribe', methods=['POST'])
def transcribe_audio():
    if 'audio' not in request.files:
        return jsonify({'error': 'No audio file provided'}), 400

    audio_file = request.files['audio']
    with tempfile.NamedTemporaryFile(suffix='.webm') as temp:
        audio_file.save(temp.name)
        result = model.transcribe(temp.name, fp16=False, language='Indonesian')
        return jsonify({'transcript': result['text']})

if __name__ == '__main__':
    app.run(debug=True, port=5000)
