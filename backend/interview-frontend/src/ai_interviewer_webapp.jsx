import { useState } from "react";
import axios from "axios";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";

const initialQuestions = [
  "Apa yang memotivasi Anda untuk bekerja di bidang ini?",
  "Ceritakan pengalaman ketika Anda berada dalam tekanan tinggi.",
  "Bagaimana Anda memastikan hasil kerja Anda tetap teliti?",
];

const categories = [
  "Motivasi kerja",
  "Stabilitas Emosi",
  "Toleransi Stress",
  "Ketelitian Kerja",
  "Tempo Kerja",
  "Orientasi Kualitas",
  "Sistematika Kerja",
  "Meyakinkan orang lain",
  "Penyesuaian Diri",
  "Kerjas<PERSON>",
  "Kepatuhan kerja",
  "Kepemimpinan"
];

export default function AIInterview() {
  const [step, setStep] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [result, setResult] = useState(null);
  const [recording, setRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);

  const handleStartRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream);
    const audioChunks = [];

    recorder.ondataavailable = (event) => {
      audioChunks.push(event.data);
    };

    recorder.onstop = async () => {
      const audioBlob = new Blob(audioChunks, { type: "audio/webm" });
      const formData = new FormData();
      formData.append("audio", audioBlob);

      try {
        const response = await axios.post("http://localhost:5000/transcribe", formData);
        const transcribedText = response.data.transcript;
        const updatedAnswers = [...answers, { question: initialQuestions[step], answer: transcribedText }];
        setAnswers(updatedAnswers);

        if (step < initialQuestions.length - 1) {
          setStep(step + 1);
        } else {
          evaluateAnswers(updatedAnswers);
        }
      } catch (error) {
        console.error("Transkripsi gagal:", error);
      }
    };

    recorder.start();
    setMediaRecorder(recorder);
    setRecording(true);
  };

  const handleStopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setRecording(false);
    }
  };

  const evaluateAnswers = () => {
    const mockScore = categories.map((cat) => {
      const value = Math.floor(Math.random() * 31) + 70;
      return { name: cat, value };
    });
    setResult(mockScore);
  };

  const getLevel = (value) => {
    if (value < 70) return "Belum memenuhi standard";
    if (value < 80) return "Memenuhi Standard Minimum";
    return "Memenuhi Standard";
  };

  return (
    <div style={{ maxWidth: 700, margin: "0 auto", padding: 24 }}>
      <h1 style={{ textAlign: "center" }}>AI Interviewer - Capture</h1>

      {!result ? (
        <div style={{ border: "1px solid #ccc", padding: 20, borderRadius: 8 }}>
          <p>{initialQuestions[step]}</p>
          {!recording ? (
            <button onClick={handleStartRecording}>Mulai Rekam</button>
          ) : (
            <button onClick={handleStopRecording}>Stop & Kirim</button>
          )}
        </div>
      ) : (
        <div style={{ marginTop: 20 }}>
          <h2>Hasil Wawancara</h2>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={result}>
              <XAxis dataKey="name" fontSize={10} interval={0} angle={-30} />
              <YAxis domain={[0, 100]} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Bar dataKey="value" />
            </BarChart>
          </ResponsiveContainer>
          <div>
            {result.map((r, idx) => (
              <p key={idx}>
                <strong>{r.name}:</strong> {r.value}% - <em>{getLevel(r.value)}</em>
              </p>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
